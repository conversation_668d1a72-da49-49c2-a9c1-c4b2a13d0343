package com.fenxiang.social.engine.base.enums.sku;

import lombok.Getter;

/**
 * TK订单状态枚举
 */
@Getter
public enum OrderTkStatus {
    // 3：订单结算，12：订单付款， 13：订单失效，14：订单成功
    PAID(12, "订单付款"),
    INVALID(13, "订单失效"),
    SUCCESS(14, "订单成功"),
    SETTLED(3, "订单结算");

    /**
     * 状态码
     */
    private final int code;
    
    /**
     * 状态描述
     */
    private final String desc;

    OrderTkStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举值，如果没找到返回null
     */
    public static OrderTkStatus getByCode(int code) {
        for (OrderTkStatus status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断状态码是否有效
     *
     * @param code 状态码
     * @return true:有效 false:无效
     */
    public static boolean isValid(Integer code) {
        if (code == null) {
            return false;
        }
        return getByCode(code) != null;
    }
}