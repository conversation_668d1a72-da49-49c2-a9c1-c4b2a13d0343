package com.fenxiang.social.engine.base.enums.statistics;

/**
 * 系统模块枚举
 * 用于定义和管理系统中的各个功能模块
 */
public enum MikeStatisticsModuleEnum {
    
    /**
     * 订单模块
     */
    ORDER("order", "省-订单"),
    /**
     * 用户模块
     */
    USER("user", "用户"),

    /**
     * 玩收入
     */
    PLAY_REVENUE("play_revenue", "玩-置顶收入"),
    /**
     * 省订单收入
     */
    ORDER_REVENUE("order_revenue", "省-订单收入"),

    /**
     * 日活统计
     *
     */
    DAU("dau", "DAU-日活")
    ;
    
    /**
     * 模块代码
     */
    private final String code;
    
    /**
     * 模块名称
     */
    private final String name;
    
    /**
     * 构造函数
     *
     * @param code 模块代码
     * @param name 模块名称
     */
    MikeStatisticsModuleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }
    
    /**
     * 获取模块代码
     *
     * @return 模块代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取模块名称
     *
     * @return 模块名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 根据代码获取枚举实例
     *
     * @param code 模块代码
     * @return 对应的枚举实例，如果不存在则返回null
     */
    public static MikeStatisticsModuleEnum getByCode(String code) {
        if (code == null) {
            return null;
        }
        
        for (MikeStatisticsModuleEnum module : MikeStatisticsModuleEnum.values()) {
            if (module.getCode().equals(code)) {
                return module;
            }
        }
        
        return null;
    }
    
    /**
     * 检查给定的代码是否是有效的模块代码
     *
     * @param code 要检查的模块代码
     * @return 如果是有效的模块代码则返回true，否则返回false
     */
    public static boolean isValidCode(String code) {
        return getByCode(code) != null;
    }
    
    /**
     * 获取所有模块代码和名称的描述
     *
     * @return 包含所有模块信息的字符串
     */
    public static String getAllModulesDescription() {
        StringBuilder builder = new StringBuilder();
        for (MikeStatisticsModuleEnum module : MikeStatisticsModuleEnum.values()) {
            builder.append(module.getCode())
                   .append(": ")
                   .append(module.getName())
                   .append(", ");
        }
        
        // 移除最后的逗号和空格
        if (builder.length() > 2) {
            builder.setLength(builder.length() - 2);
        }
        
        return builder.toString();
    }

    public static boolean isPlayModule(String moduleCode) {
        return PLAY_REVENUE.getCode().equals(moduleCode);
    }

    public static boolean isOrderModule(String moduleCode) {
        return ORDER_REVENUE.getCode().equals(moduleCode);
    }
}