package com.fenxiang.social.engine.base.enums;

import java.util.Arrays;

/**
 * @Description 近多少天最低价标签枚举
 * @Date 2023/4/25 18:17
 * @Created by renchengpeng
 */
public enum LatestDaysMinPriceLabelEnum {

    LATEST_DAYS_LABEL_30(30, 30, 59, "近30天最低价"),
    LATEST_DAYS_LABEL_60(60, 60, 89, "近60天最低价"),
    LATEST_DAYS_LABEL_90(90, 90, 179, "近90天最低价"),
    LATEST_DAYS_LABEL_180(180, 180, Integer.MAX_VALUE, "近180天最低价"),
    LATEST_DAYS_LABEL_BREAK_PRICE_90(-90, -179, -90, "破价新低90天"),
    LATEST_DAYS_LABEL_BREAK_PRICE_180(-180, -364, -180, "破价新低180天"),
    LATEST_DAYS_LABEL_BREAK_PRICE_365(-365, Integer.MIN_VALUE, -365, "破价新低365天");
    /**
     * 标签名
     */
    public final Integer labelName;

    /**
     * 标签对应的最近多少天数的起始天数
     */
    public final Integer startDay;
    /**
     * 标签对应的最近多少天数的结束天数
     */
    public final Integer endDay;
    /**
     * 最低价
     */
    public final String desc;

    /**
     * 构造方法
     */
    LatestDaysMinPriceLabelEnum(Integer labelName, Integer startDay, Integer endDay, String msg) {
        this.labelName = labelName;
        this.startDay = startDay;
        this.endDay = endDay;
        this.desc = msg;
    }

    public static LatestDaysMinPriceLabelEnum getByLabelName(Integer labelName) {
        return Arrays.stream(values()).filter(item -> item.labelName.equals(labelName)).findFirst().orElse(null);
    }
}
