package com.fenxiang.social.engine.base.enums;

/**
 * 内容安全业务类型
 */
public enum QqContentSecurityBusinessTypeEnum {

    POST_CONTENT(10, "资讯文本"),
    POST_COVER_IMAGE(20, "资讯封面图"),
    POST_CONTENT_IMAGE(30, "资讯内容图"),
    POST_COMMENT_CONTENT(40, "资讯评论文本"),

    ITEM_CONTENT(50, "爆料文本"),
    ITEM_CONTENT_IMAGE(60, "爆料内容图"),
    ;

    public final Integer code;

    public final String desc;

    QqContentSecurityBusinessTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
