package com.fenxiang.social.engine.base.enums;

import lombok.ToString;

/**
 * 用户角色枚举
 */
@ToString
public enum QqUserRoleEnum {
    /**
     * 超级会员
     */
    VIP(10, "超级会员"),
    /**
     * 导师
     */
    BUSINESS(20, "导师"),
    /**
     * 合伙人
     */
    PARTNER(50, "合伙人");
    /**
     * 分佣佣金类型
     */
    public final Integer role;
    /**
     * 分佣佣金类型描述
     */
    public final String msg;


    QqUserRoleEnum(Integer role, String msg) {
        this.role = role;
        this.msg = msg;
    }

    /**
     * 判断是否为导师级用户
     *
     * @param role 用户角色
     * @return true:是；false:否
     */
    public static boolean isBusinessLevel(Integer role) {
        return (BUSINESS.role.equals(role) || PARTNER.role.equals(role));
    }

    /**
     * 判断是否为超级会员用户
     *
     * @param role 用户角色
     * @return true:是；false:否
     */
    public static boolean isVip(Integer role) {
        return VIP.role.equals(role);
    }

    /**
     * 判断是否为超级会员用户级
     *
     * @param role 用户角色
     * @return true:是；false:否
     */
    public static boolean geVipLevel(Integer role) {
        return VIP.role <= role;
    }

    /**
     * 判断是否为导师
     *
     * @param role 用户角色
     * @return true:是；false:否
     */
    public static boolean isBusiness(Integer role) {
        return BUSINESS.role.equals(role);
    }

    /**
     * 判断是否为合伙人
     *
     * @param role 用户角色
     * @return true:是；false:否
     */
    public static boolean isPartner(Integer role) {
        return PARTNER.role.equals(role);
    }

}
