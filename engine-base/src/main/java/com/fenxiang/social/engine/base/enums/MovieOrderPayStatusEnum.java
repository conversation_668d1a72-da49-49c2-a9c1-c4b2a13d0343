package com.fenxiang.social.engine.base.enums;

/**
 * 新联国会议员订单支付情况
 *
 * <AUTHOR>
 * @date 2024/03/07
 */
public enum MovieOrderPayStatusEnum {


    /**
     * 电影订单状态
     */
    GENERATE_SUCCESS("GENERATE_SUCCESS", "已出票"),
    WAIT_GENERATE("WAIT_GENERATE", "待出票"),
    REFUNDED("REFUNDED", "已取消"),
    WAIT_PAY("WAIT_PAY", "待支付"),
    CLOSE("CLOSE", "超时未支付关闭");

    /**
     * 类型
     */
    public final String type;
    /**
     * 味精
     */
    public final String msg;

    /**
     * 新联订单微信支付现状
     *
     * @param type 类型
     * @param msg  味精
     */
    MovieOrderPayStatusEnum(String type, String msg) {
        this.type = type;
        this.msg = msg;
    }
    public static MovieOrderPayStatusEnum fromType(String type) {
        if (type == null) {
            return null;
        }
        for (MovieOrderPayStatusEnum commissionIncomeTypeEnum : MovieOrderPayStatusEnum.values()) {
            if (commissionIncomeTypeEnum.type.equals(type)) {
                return commissionIncomeTypeEnum;
            }
        }
        return null;
    }
}
