package com.fenxiang.social.engine.base.enums;

/**
 * 资讯排序规则
 */
public enum QqPostCommentStatusEnum {

    WAIT_SYSTEM_AUDIT(0, "待机器审核"),
    AUDIT_PASS(20, "审核通过"),
    AUDIT_REJECT(30, "审核驳回"),
    DELETED(40, "已删除");

    public final Integer code;

    public final String desc;

    QqPostCommentStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean isAuditPass(Integer code){
        return AUDIT_PASS.code.equals(code);
    }
}
