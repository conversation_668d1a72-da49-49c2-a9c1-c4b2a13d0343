package com.fenxiang.social.engine.base.enums;

import java.util.Arrays;

/**
 * @Description 用户角色
 * @Date 2023/7/13 14:15
 * @Created by renchengpeng
 */
public enum UserRoleEnum {

    PUSHER(0, "推手"),
    LESSOR(1, "出借人");

    public final Integer role;

    public final String roleName;

    UserRoleEnum(Integer role, String roleName) {
        this.role = role;
        this.roleName = roleName;
    }

    public static UserRoleEnum getByRole(Integer role) {
        return Arrays.stream(UserRoleEnum.values()).filter(userRoleEnum -> userRoleEnum.role.equals(role)).findFirst().orElse(null);
    }

}
