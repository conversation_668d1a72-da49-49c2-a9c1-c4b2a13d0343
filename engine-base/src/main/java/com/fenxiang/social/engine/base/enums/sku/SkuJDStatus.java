package com.fenxiang.social.engine.base.enums.sku;

public enum SkuJDStatus {
    UNKNOWN(-1, "未知"),
    INVALID_SPLIT_ORDER(2, "无效-拆单"),
    INVALID_CANCELLED(3, "无效-取消"),
    INVALID_JD_HELPER_ORDER(4, "无效-京东帮帮主订单"),
    INVALID_ACCOUNT_EXCEPTION(5, "无效-账号异常"),
    INVALID_GIFT_CATEGORY_NO_COMMISSION(6, "无效-赠品类目不返佣"),
    INVALID_CAMPUS_ORDER(7, "无效-校园订单"),
    INVALID_ENTERPRISE_ORDER(8, "无效-企业订单"),
    INVALID_GROUP_ORDER(9, "无效-团购订单"),
    INVALID_RURAL_PROMOTER_ORDER(11, "无效-乡村推广员下单"),
    VIOLATION_ORDER_OTHER(13, "违规订单-其他"),
    INVALID_SOURCE_MISMATCH(14, "无效-来源与备案网址不符"),
    PENDING_PAYMENT(15, "待付款"),
    PAID(16, "已付款"),
    COMPLETED(17, "已完成（购买用户确认收货）"),
    INVALID_COMMISSION_ZERO(19, "无效-佣金比例为0"),
    INVALID_REORDER_FIRST_INVALID(20, "无效-此复购订单对应的首购订单无效"),
    INVALID_CLOUD_STORE_ORDER(21, "无效-云店订单"),
    INVALID_PLUS_MEMBER_COMMISSION_ZERO(22, "无效-PLUS会员佣金比例为0"),
    INVALID_PAY_GIFT(23, "无效-支付有礼"),
    PAID_DEPOSIT(24, "已付定金"),
    VIOLATION_TRAFFIC_HIJACK(25, "违规订单-流量劫持"),
    VIOLATION_TRAFFIC_ABNORMAL(26, "违规订单-流量异常"),
    VIOLATION_JD_RULES(27, "违规订单-违反京东平台规则"),
    VIOLATION_MULTIPLE_TRANSACTION_ABNORMAL(28, "违规订单-多笔交易异常"),
    INVALID_CROSS_SCREEN_SHOP(29, "无效-跨屏跨店"),
    INVALID_EXCEED_CATEGORY_LIMIT(30, "无效-累计件数超出类目上限"),
    INVALID_BLACKLIST_SKU(31, "无效-黑名单sku"),
    SUPERMARKET_CARD_RECHARGE(33, "超市卡充值订单"),
    INVALID_PUSH_CARD_ORDER(34, "无效-推卡订单无效");

    private final int code;
    private final String desc;

    SkuJDStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static SkuJDStatus fromCode(int code) {
        for (SkuJDStatus status : SkuJDStatus.values()) {
            if (status.code == code) {
                return status;
            }
        }
        return UNKNOWN;
    }
}