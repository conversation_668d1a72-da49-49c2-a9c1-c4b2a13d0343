package com.fenxiang.social.engine.base.enums;

/**
 * 位置
 */

public enum QqPositionEnum {

    WAN(10, "玩"),
    ZHUAN(20, "赚"),
    SHENG(30, "省");

    public final Integer id;
    public final String text;


    QqPositionEnum(Integer id, String text) {
        this.id = id;
        this.text = text;
    }

    public static String idToText(Integer id) {
        if (id == null) {
            return null;
        }
        for (QqPositionEnum position : QqPositionEnum.values()) {
            if (id.equals(position.id)) {
                return position.text;
            }
        }
        return null;
    }
}
