package com.fenxiang.social.engine.base.enums;


import java.util.Arrays;
import java.util.Optional;

/**
 * @Description 提现标识枚举
 * <AUTHOR>
 * @date 2023/6/20
 **/
public enum WithdrawFlagEnum {
    WITHDRAW_ENABLE(0, "可提现"),
    PROCESSING(1, "处理中"),
    INSUFFICIENT_BALANCE(2, "余额不足，无法提现"),
    WINDOW_CLOSED(3, "当前日期不在提现窗口内"),
    FAILED(4, "提现失败"),
    NOT_REACH_BOTTOM_AMOUNT(5, "当前账户余额无法提现"),
    ;
    public final Integer code;
    public final String desc;

    WithdrawFlagEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static WithdrawFlagEnum codeOf(int code) {
        Optional<WithdrawFlagEnum> optional = Arrays.stream(values()).filter(withdrawFlagEnum -> withdrawFlagEnum.code.intValue() == code).findFirst();
        return optional.isPresent() ? optional.get() : null;
    }
}