package com.fenxiang.social.engine.base.enums;

import lombok.Getter;

/**
 * 平台
 */
@Getter
public enum QqPlatformEnum {

    HOT("hot","热门"),
    JD("jd","京东"),
    TB("tb", "淘宝"),
    PDD("pdd", "拼多多"),
    DY("dy", "抖音"),
    HW_BURGER_KING("bgk", "汉堡王"),
    HW_KFC("kfc", "肯德基"),
    HW_MC("mcd", "麦当劳"),
    HW_STARBUCKS("starbucks", "星巴克"),
    HW_CHAGEE("chagee", "霸王茶姬"),
    HW_COTTI("cotti", "库迪"),
    HW_HEYTEA("heytea", "喜茶"),
    HW_LUCKIN("luckin", "瑞幸"),
    HW_MOVIE("movie", "电影票"),
    HW_NAYUKI("nayuki", "奈雪的茶"),
    HW_PIZZA_HUT("piz", "必胜客"),
    HW_VIP("vip", "VIP卡券"),
    HW_WALLACE("wallace", "华莱士"),
    HW_EXPRESS("express", "比价寄"),
    XXHUI_RECHARGE("xxh", "话费充值"),
    MEITUAN("meituan", "美团"),
    ELEME("eleme", "饿了么"),
    ;

    public final String code;

    public final String desc;

    QqPlatformEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static QqPlatformEnum fromCode(String code) {
        for (QqPlatformEnum platformEnum : QqPlatformEnum.values()) {
            if (platformEnum.code.equals(code)) {
                return platformEnum;
            }
        }
        return null;
    }
}
