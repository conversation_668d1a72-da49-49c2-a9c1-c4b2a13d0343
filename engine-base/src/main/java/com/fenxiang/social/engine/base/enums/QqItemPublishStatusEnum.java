package com.fenxiang.social.engine.base.enums;

/**
 * 资讯发布状态
 */
public enum QqItemPublishStatusEnum {

    DRAFT(0,"草稿"),
    WAIT_PAY(10,"待支付"),
    WAIT_SYSTEM_AUDIT(20, "待机器审核"),
    WAIT_MANUAL_AUDIT(30, "待人工审核"),
    PUBLISHING(40, "发布中"),
    AUDIT_REJECT(50, "审核驳回"),
    CLOSED(60, "已终止"),
    DELETED(70, "已删除");

    public final Integer code;

    public final String desc;

    QqItemPublishStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
