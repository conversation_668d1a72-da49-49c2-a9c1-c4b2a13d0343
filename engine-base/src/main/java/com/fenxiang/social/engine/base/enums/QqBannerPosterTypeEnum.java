package com.fenxiang.social.engine.base.enums;

import java.util.Arrays;


public enum QqBannerPosterTypeEnum {

    IMAGE(10, "图片"),
    VIDEO(20, "视频");

    public final Integer type;

    public final String desc;

    QqBannerPosterTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static QqBannerPosterTypeEnum of(Integer type) {
        if (type == null) {
            return null;
        }
        return Arrays.stream(QqBannerPosterTypeEnum.values()).filter(e -> e.type.equals(type)).findFirst().orElse(null);
    }
}
