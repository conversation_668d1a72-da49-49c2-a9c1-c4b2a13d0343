package com.fenxiang.social.engine.base.enums;

/**
 * 生活服务佣金明细订单有效状态
 *
 * <AUTHOR>
 * @date 2020/6/22 19:53
 */
public enum LifeCommissionDetailOrderValidStatusEnum {

    VALID(1, "有效"),
    INVALID(2, "无效");

    public final Integer type;
    public final String validStatusName;

    LifeCommissionDetailOrderValidStatusEnum(Integer type, String validStatusName) {
        this.type = type;
        this.validStatusName = validStatusName;
    }
}
