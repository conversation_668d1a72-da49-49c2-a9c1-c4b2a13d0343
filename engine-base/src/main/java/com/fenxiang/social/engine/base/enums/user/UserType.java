package com.fenxiang.social.engine.base.enums.user;

import lombok.Getter;

@Getter
public enum UserType {
    NORMAL_USER(1, "普通用户"),
    SCHOOL_VIRTUAL_USER(2, "学校虚拟用户");

    private final int code;
    private final String desc;

    UserType(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    // 根据code获取枚举
    public static UserType fromCode(int code) {
        for (UserType type : UserType.values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static boolean isNormalUser(int code) {
        return code == NORMAL_USER.getCode();
    }

    public static boolean isSchoolVirtualUser(int code) {
        return code == SCHOOL_VIRTUAL_USER.getCode();
    }
}