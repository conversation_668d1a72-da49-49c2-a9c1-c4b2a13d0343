package com.fenxiang.social.engine.base.enums.mobileRecharge;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 电费验证三要素类型枚举
 */
@Getter
@AllArgsConstructor
public enum RechargeVerifyTypeEnum {
    
    ID_CARD(1, "身份证后6位"),
    BANK_CARD(2, "银行卡后六位"),
    BUSINESS_LICENSE(3, "营业执照后六位");

    /**
     * 类型编码
     */
    private final Integer code;
    
    /**
     * 类型描述
     */
    private final String desc;

    /**
     * 根据code获取枚举
     */
    public static RechargeVerifyTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RechargeVerifyTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }
}