package com.fenxiang.social.engine.base.enums.mobileRecharge;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum RechargeType {
    PHONE_SLOW(1, "话费慢充", false),
    PHONE_SLOW_REAL_NAME(2, "话费慢充-带实名信息", true),
    ELECTRIC_SLOW_REAL_NAME(3, "电费慢充-带实名信息", true)
    ;

    private final int code;
    private final String desc;
    private final boolean needRealName;


    /**
     * 通过产品名称匹配充值类型
     * 匹配规则：
     * 1. 优先匹配精确描述
     * 2. 如果没有精确匹配，则通过关键字匹配
     */
    public static RechargeType matchByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return null;
        }

        // 1. 精确匹配描述
        for (RechargeType type : values()) {
            if (type.getDesc().equals(name)) {
                return type;
            }
        }

        // 2. 关键字匹配
        boolean isPhone = name.contains("话费");
        boolean isElectric = name.contains("电费");
        boolean isRealName = name.contains("实名") || name.contains("带实名信息");

        if (isPhone) {
            return isRealName ? PHONE_SLOW_REAL_NAME : PHONE_SLOW;
        } else if (isElectric) {
            return ELECTRIC_SLOW_REAL_NAME;
        }
        return null;
    }



    public static RechargeType fromCode(int code) {
        for (RechargeType type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }

}
