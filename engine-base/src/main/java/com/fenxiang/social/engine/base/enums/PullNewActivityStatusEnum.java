package com.fenxiang.social.engine.base.enums;

import java.util.Arrays;

/**
 * 拉新活动状态
 */
public enum PullNewActivityStatusEnum {

    NOT_STARTED(0, "未开始"),
    ONGOING(1, "进行中"),
    FINISHED(2, "已结束"),
    ;

    public final Integer status;

    public final String desc;

    PullNewActivityStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static PullNewActivityStatusEnum statusOf(Integer status) {
        if (status == null) {
            return null;
        }
        return Arrays.stream(PullNewActivityStatusEnum.values())
                .filter(activityStatusEnum -> activityStatusEnum.status.equals(status))
                .findFirst()
                .orElse(null);
    }

}
