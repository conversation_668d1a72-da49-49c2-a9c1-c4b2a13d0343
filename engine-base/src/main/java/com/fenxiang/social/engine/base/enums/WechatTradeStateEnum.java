package com.fenxiang.social.engine.base.enums;

/**
 * 微信支付状态
 */
public enum WechatTradeStateEnum {
    SUCCESS("SUCCESS", "支付成功"),
    REFUND("REFUND", "转入退款"),
    NOTPAY("NOTPAY", "未支付"),
    CLOSED("CLOSED", "已关闭"),
    REVOKED("REVOKED", "已撤销（仅付款码支付会返回）"),
    USERPAYING("USERPAYING", "用户支付中（仅付款码支付会返回）"),
    PAYERROR("PAYERROR", "支付失败（仅付款码支付会返回）");
    public final String code;

    public final String desc;

    WechatTradeStateEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
