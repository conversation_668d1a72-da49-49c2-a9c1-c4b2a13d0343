package com.fenxiang.social.engine.base.enums;

/**
 * 联系方式
 */
public enum QqPostContactTypeEnum {

    NONE(0, "无"),
    QQ(10, "QQ"),
    WECHAT(20, "微信"),
    <PERSON><PERSON><PERSON><PERSON>(30, "手机号"),
    <PERSON><PERSON><PERSON>(40, "其他");

    public final Integer code;

    public final String desc;

    QqPostContactTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public static String code2DescWithoutNone(Integer code) {
        for (QqPostContactTypeEnum qqPostContactTypeEnum : QqPostContactTypeEnum.values()) {
            if (NONE.code.equals(code)) {
                return "";
            }
            if (qqPostContactTypeEnum.code.equals(code)) {
                return qqPostContactTypeEnum.desc;
            }
        }
        return "";
    }

}
