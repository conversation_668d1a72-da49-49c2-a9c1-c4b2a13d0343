package com.fenxiang.social.engine.base.enums;

/**
 * 微信支付渠道
 */
public enum QqWechatPayChannelEnum {
    JSAPI("JSAPI", "公众号支付、小程序支付"),
    APP("APP", "APP支付"),
    QQ_WECHAT_H5("QQ_WECHAT_H5", "QQ小程序微信H5支付");

    public final String code;

    public final String desc;

    QqWechatPayChannelEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean isValid(String code) {
        if (code == null) {
            return false;
        }
        for (QqWechatPayChannelEnum wechatPayChannel : QqWechatPayChannelEnum.values()) {
            if (wechatPayChannel.code.equals(code)) {
                return true;
            }
        }
        return false;
    }
}
