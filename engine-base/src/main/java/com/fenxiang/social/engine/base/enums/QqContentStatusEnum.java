package com.fenxiang.social.engine.base.enums;

/**
 * qq 内容状态枚举
 *
 * <AUTHOR>
 * @date 2024/08/15
 */
public enum QqContentStatusEnum {

    /**
     * 审核装套
     */
    AUDIT_ING(0L, "审核中"),
    AUDIT_WAIT_SEND(1L, "审核成功-待发送"),
    AUDIT_SEND_SUCCESS(2L, "审核成功-已发送"),
    AUDIT_SEND_FAIL(3L, "审核成功-发送失败"),
    AUDIT_FAIL(4L, "审核失败");

    public final Long status;

    public final String desc;

    QqContentStatusEnum(Long status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
