package com.fenxiang.social.engine.base.enums;

/**
 * 管理员类型
 */
public enum QqAdminTypeEnum {

    USER(0, "用户"),
    SUPER_ADMIN(10, "超级管理员"),
    SCHOOL_SERVICE_PROVIDER(20, "校园服务商"),
    SCHOOL_ADMIN(30, "校园管理员"),
    MEDIA_MASTER(40, "流量主"),
    PROXY(99, "虚拟用户"),
    ;

    public final Integer code;

    public final String desc;

    QqAdminTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean isUser(Integer adminType){
        return USER.code.equals(adminType);
    }

    public static boolean isSchoolAdmin(Integer adminType){
        return SCHOOL_ADMIN.code.equals(adminType);
    }

    public static boolean isMediaMaster(Integer adminType){
        return MEDIA_MASTER.code.equals(adminType);
    }

    public static boolean isSuperAdmin(Integer adminType){
        return SUPER_ADMIN.code.equals(adminType);
    }

    public static boolean isServiceProvider(Integer adminType){
        return SCHOOL_SERVICE_PROVIDER.code.equals(adminType);
    }
}
