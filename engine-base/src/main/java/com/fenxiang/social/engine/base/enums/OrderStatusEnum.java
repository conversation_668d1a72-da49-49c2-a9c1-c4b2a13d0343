package com.fenxiang.social.engine.base.enums;

import java.util.Arrays;

/**
 * 订单状态
 */
public enum OrderStatusEnum {

    UNPAID(15, "待付款"),
    PAID(16, "已付款"),
    COMPLETED(17, "已完成");

    public final Integer status;

    public final String desc;

    OrderStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static OrderStatusEnum statusOf(Integer status) {
        if (status == null) {
            return null;
        }
        return Arrays.stream(OrderStatusEnum.values()).filter(orderStatusEnum -> orderStatusEnum.status.equals(orderStatusEnum)).findFirst().orElse(null);
    }
}
