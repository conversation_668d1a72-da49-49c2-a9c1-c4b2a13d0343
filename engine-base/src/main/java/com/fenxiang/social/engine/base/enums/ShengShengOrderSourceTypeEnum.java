package com.fenxiang.social.engine.base.enums;

/**
 * 爱省生活 订单源类型列举
 */
public enum ShengShengOrderSourceTypeEnum {

    /**
     * 京东
     */
    JD(1, "京东"),
    TB(2, "淘宝"),
    PDD(3, "拼多多");

    public final Integer type;

    public final String name;

    ShengShengOrderSourceTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public int getType() {
        return type;
    }

    public static String getNameByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (ShengShengOrderSourceTypeEnum commissionIncomeTypeEnum : ShengShengOrderSourceTypeEnum.values()) {
            if (commissionIncomeTypeEnum.type.equals(type)) {
                return commissionIncomeTypeEnum.name;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }

}
