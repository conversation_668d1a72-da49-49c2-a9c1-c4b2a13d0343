package com.fenxiang.social.engine.base.enums;


import lombok.Data;
import lombok.Getter;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;

/**
 * @Description 平台枚举
 * <AUTHOR>
 * @date 2023/9/19
 **/
public enum PlatformEnum {

    JD("jd", "京东"),
    TB("tb", "淘宝"),
    VOP("vop", "唯品会"),
    PDD("pdd", "拼多多"),
    JXT("jxt", "京喜团"),
    DTK("dtk", "大淘客"),
    HW_BURGER_KING("bgk", "汉堡王"),
    HW_MOVIE("movie", "电影票"),
    HW_VIP("vip", "VIP卡券")
    ;
    public final String type;
    public final String desc;

    PlatformEnum(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static PlatformEnum typeOf(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }

        return Arrays.stream(values()).filter(e -> type.equals(e.type)).findFirst().orElse(null);
    }
}