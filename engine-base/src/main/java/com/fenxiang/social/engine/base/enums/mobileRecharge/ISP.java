package com.fenxiang.social.engine.base.enums.mobileRecharge;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ISP {
    //1移动,2电信,3联通,4虚拟
    CHINA_MOBILE(1, "中国移动","https://mike-1314457071.cos.ap-beijing.myqcloud.com/pic/e560d6f6cbd24ac482898ec2c95c6a95.png"),
    CHINA_TELECOM(2, "中国电信", "https://mike-1314457071.cos.ap-beijing.myqcloud.com/pic/9c19fa96295845f39e765c3a69c7f522.png"),
    CHINA_UNICOM(3, "中国联通", "https://mike-1314457071.cos.ap-beijing.myqcloud.com/pic/aaabf706ddd04365a1ed8d82801ed870.png"),
    VIRTUAL(4, "虚拟", ""),
    ;

    private int code;
    private String desc;
    private String imageUrl;
    private String name;

    ISP(int code, String desc, String imageUrl) {
        this.code = code;
        this.desc = desc;
        this.imageUrl = imageUrl;
        name = name();
    }

    @JsonCreator
    public static ISP fromCode(int code) {
        for (ISP value : ISP.values()) {
            if (value.code == code) {
                return value;
            }
        }
        return null;
    }

    public static ISP fromDesc(String desc) {
        for (ISP value : ISP.values()) {
            if (value.desc.contains(desc)) {
                return value;
            }
        }
        return null;
    }
}
