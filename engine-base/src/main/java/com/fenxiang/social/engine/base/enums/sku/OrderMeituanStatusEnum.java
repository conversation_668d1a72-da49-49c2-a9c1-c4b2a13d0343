package com.fenxiang.social.engine.base.enums.sku;

import com.fenxiang.social.engine.base.enums.QqShengOrderStatusEnum;
import lombok.Getter;

/**
 * 订单维度状态枚举
 */
@Getter
public enum OrderMeituanStatusEnum {
    
    PAID(2, "付款", "已付款/CPA订单奖励已创建"),
    COMPLETED(3, "完成", "订单完成"),
    CANCELLED(4, "取消", "订单取消"),
    RISK_CONTROL(5, "风控", "风控状态"),
    SETTLED(6, "结算", "结算完成"),
    ;

    private final int code;
    private final String name;
    private final String desc;

    OrderMeituanStatusEnum(int code, String name, String desc) {
        this.code = code;
        this.name = name;
        this.desc = desc;
    }

    /**
     * 根据状态码获取枚举
     */
    public static OrderMeituanStatusEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (OrderMeituanStatusEnum status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 校验是否为有效的状态码
     */
    public static boolean isValidCode(Integer code) {
        return getByCode(code) != null;
    }

    /**
     * 判断是否为终态
     */
    public boolean isFinalStatus() {
        return this == COMPLETED || this == CANCELLED || this == RISK_CONTROL || this == SETTLED;
    }

    /**
     * 判断是否为CPA订单可用状态
     * 仅到家闪购订单、到家医药订单、到店到餐、到综业务类型有结算状态
     */
    public boolean isValidForCpaOrder() {
        return this != SETTLED;
    }

    public static QqShengOrderStatusEnum toQqShengOrderStatus(OrderMeituanStatusEnum status) {
        if (status == null){
            return QqShengOrderStatusEnum.INVALID;
        }
        switch (status) {
            case PAID:
                return QqShengOrderStatusEnum.PAY_SUCCESS;
            case COMPLETED:
            case SETTLED:
                return QqShengOrderStatusEnum.FINISHED;
            case CANCELLED:
            case RISK_CONTROL:
                return QqShengOrderStatusEnum.INVALID;
        }
        return QqShengOrderStatusEnum.INVALID;
    }
}