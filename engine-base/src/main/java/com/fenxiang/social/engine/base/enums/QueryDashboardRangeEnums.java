package com.fenxiang.social.engine.base.enums;


import org.apache.commons.lang.StringUtils;

import java.util.Arrays;

/**
 * @Description 查询大屏时间范围
 * <AUTHOR>
 * @date 2023/6/20
 **/
public enum QueryDashboardRangeEnums {
    TODAY("TODAY", "今天"),
    THIS_MONTH("THIS_MONTH", "本月"),
    ;
    public final String type;
    public final String desc;

    QueryDashboardRangeEnums(String type, String desc) {
        this.type = type;
        this.desc = desc;
    }


    public static QueryDashboardRangeEnums typesOf(String type) {
        if (StringUtils.isBlank(type)) {
            return null;
        }

        return Arrays.stream(values()).filter(e -> e.type.equalsIgnoreCase(type)).findFirst().orElse(null);
    }
}