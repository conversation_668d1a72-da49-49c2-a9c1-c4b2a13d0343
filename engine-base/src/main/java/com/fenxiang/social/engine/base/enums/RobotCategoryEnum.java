package com.fenxiang.social.engine.base.enums;


import java.util.Arrays;

public enum RobotCategoryEnum {

    WECHAT(1, "微信机器人"),
    WECOM(2, "企微机器人");

    public int type;
    public String desc;

    RobotCategoryEnum(int nType, String desc) {
        this.type = nType;
        this.desc = desc;
    }

    public int getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static RobotCategoryEnum typesOf(int type) {
        return Arrays.stream(RobotCategoryEnum.values())
                .filter(robotCategoryEnum -> robotCategoryEnum.getType() == type)
                .findFirst().orElse(null);
    }

}
