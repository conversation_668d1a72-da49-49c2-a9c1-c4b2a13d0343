package com.fenxiang.social.engine.base.enums;

/**
 * 支付方式
 */
public enum QqPayTypeEnum {
    WECHAT("weixin", "微信支付");

    public final String code;

    public final String desc;

    QqPayTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean isValid(String code) {
        if (code == null) {
            return false;
        }
        for (QqPayTypeEnum payType : QqPayTypeEnum.values()) {
            if (payType.code.equals(code)) {
                return true;
            }
        }
        return false;
    }
}
