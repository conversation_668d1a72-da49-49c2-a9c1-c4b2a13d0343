package com.fenxiang.social.engine.base.enums;


import java.util.Arrays;

/**
 * @Description 助手出租类型
 * <AUTHOR>
 * @date 2023/8/21
 **/
public enum RobotRentTypeEnum {
    PRIVATE(1, "按号出租"),

    SHARE(2, "按群出租"),

    ;
    public final Integer code;
    public final String desc;

    RobotRentTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RobotRentTypeEnum codesOf(Integer code) {
        if (code == null) {
            return null;
        }
        return Arrays.asList(values()).stream().filter(r -> r.code.intValue() == code.intValue()).findFirst().orElse(null);
    }
}