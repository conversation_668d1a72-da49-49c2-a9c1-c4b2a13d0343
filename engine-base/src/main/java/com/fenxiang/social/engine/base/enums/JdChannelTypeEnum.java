package com.fenxiang.social.engine.base.enums;

import lombok.ToString;

/**
 * JD 频道枚举
 *
 * <AUTHOR>
 * @date 2024/08/14
 */
@ToString
public enum JdChannelTypeEnum {
    /**
     * 键盘
     */
    DEFAULT(0L, "默认-人工录入"),
    BEST_COUPON(1L, "好券商品"),
    FINE(2L, "精选卖场"),
    LOWER_PRICE(10L, "9.9包邮"),
    /* -- 芬香频道 --*/
    FX_channel_1(70L,"精选爆品"),
    FX_channel_2(71L,"食品健康"),
    FX_channel_3(72L,"家电百货"),
    SUBSIDY(12228L, "百亿补贴");
    /**
     * 查询选择
     */
    public final Long role;
    /**
     * 描述
     */
    public final String msg;


    JdChannelTypeEnum(Long role, String msg) {
        this.role = role;
        this.msg = msg;
    }

    public Long getRole() {
        return role;
    }

    public String getMsg() {
        return msg;
    }
}
