package com.fenxiang.social.engine.base.enums;

/**
 * 优惠方式
 */
public enum QqItemPromotionStyleEnum {

    FULL_AMOUNT_DISCOUNT_COUPON(101, "满x元减y元优惠券", "【优惠券】满%s元减%s元"),
    FULL_QUANTITY_DISCOUNT_COUPON(102, "满x件减y元优惠券", "【优惠券】满%s件减%s元"),
    FULL_AMOUNT_DISCOUNT_RATE_COUPON(103, "满x元打y折优惠券", "【优惠券】满%s元打%s折"),
    FULL_QUANTITY_DISCOUNT_RATE_COUPON(104, "满x件打y折优惠券", "【优惠券】满%s件打%s折"),
    FULL_AMOUNT_DISCOUNT_ACTIVITY(201, "满x元减y元满减活动", "【活动】满%s元减%s元"),
    FULL_QUANTITY_DISCOUNT_ACTIVITY(202, "满x件减y元满减活动", "【活动】满%s件减%s元"),
    FULL_AMOUNT_DISCOUNT_RATE_ACTIVITY(203, "满x元打y折满减活动", "【活动】满%s元打%s折"),
    FULL_QUANTITY_DISCOUNT_RATE_ACTIVITY(204, "满x件打y折满减活动", "【活动】满%s件打%s折");

    public final Integer code;

    public final String desc;

    public final String textTemplate;

    QqItemPromotionStyleEnum(Integer code, String desc, String textTemplate) {
        this.code = code;
        this.desc = desc;
        this.textTemplate = textTemplate;
    }

    public static String code2TextTemplate(Integer code){
        for (QqItemPromotionStyleEnum itemPromotionStyleEnum : QqItemPromotionStyleEnum.values()) {
            if(itemPromotionStyleEnum.code.equals(code)){
                return itemPromotionStyleEnum.textTemplate;
            }
        }
        return null;
    }
}
