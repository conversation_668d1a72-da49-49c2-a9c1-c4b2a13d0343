package com.fenxiang.social.engine.base.enums;

import java.util.Arrays;

/**
 *
 */
public enum QqContentContactTypeEnum {

    /**
     * 0 QQ
     * 1 微信
     * 2 手机号
     */
    QQ(0, "QQ号"),
    WECHAT(1, "微信号"),
    PHONE(2, "手机号");

    public final Integer type;

    public final String desc;

    QqContentContactTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getByType(Integer type) {
        return Arrays.stream(values()).filter(item -> item.type.equals(type)).findFirst().get().desc;
    }
}
