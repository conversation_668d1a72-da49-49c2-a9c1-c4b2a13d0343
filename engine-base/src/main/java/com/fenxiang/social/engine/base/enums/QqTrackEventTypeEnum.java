package com.fenxiang.social.engine.base.enums;

import java.util.Objects;

/**
 * 资讯事件类型
 */
public enum QqTrackEventTypeEnum {

    POST_CLICK(1001, "玩点击"),
    POST_SHARE(1002, "玩分享"),
    EARN_CLICK(1101, "赚点击"),
    EARN_SHARE(1102, "赚分享"),
    MINI_PROGRAM(2001, "打开小程序"),
    APP(2002, "打开APP"),
    ;

    public final Integer code;

    public final String desc;

    QqTrackEventTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean isDAU(Integer code){
        return Objects.equals(code, MINI_PROGRAM.code)
                || Objects.equals(code, APP.code);
    }
}
