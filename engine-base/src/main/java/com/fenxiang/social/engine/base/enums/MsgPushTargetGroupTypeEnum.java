package com.fenxiang.social.engine.base.enums;


import java.util.Arrays;

/**
 * @Description 消息推送目标群类型
 * <AUTHOR>
 * @date 2023/10/16
 **/
public enum MsgPushTargetGroupTypeEnum {

    ALL(1, "全部顾客群"),
    OFFICIAL_GROUP(2, "平台官方群"),
    RECOMMEND_GROUP(3, "类目推荐群（跟发我们平台置顶的推荐顾客群，商品对应类目）"),
    CATEGORY_GROUP(4, "类目群（跟发我们榜单上的类目群的顾客群，商品对应类目）"),
    ALL_TOP_GROUP(5, "全部榜单群（跟发我们平台上榜单群的顾客群）")
    ;

    public final Integer type;
    public final String desc;

    MsgPushTargetGroupTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static MsgPushTargetGroupTypeEnum typeOf(Integer type) {
        if (type == null) {
            return null;
        }

        return Arrays.stream(values()).filter(e -> type.equals(e.type)).findFirst().orElse(null);
    }
}