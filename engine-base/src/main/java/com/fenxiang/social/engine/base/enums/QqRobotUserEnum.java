package com.fenxiang.social.engine.base.enums;

/**
 * qq机器人用户enum
 *
 * <AUTHOR>
 * @date 2024/08/28
 */
public enum QqRobotUserEnum {
    /**
     * 审核
     */
    LOGIN_INIT(0L, "初始化添加"),
    LOGIN_ING(1L, "登陆发送"),
    LOGIN_TICKET(2L, "登陆需要滑块"),
    LOGIN_SMS(3L, "登陆需要短信验证"),
    LOGIN_SUCCESS(4L, "登陆成功"),
    LOGIN_FAIL(5L, "登陆失败"),
    LOGOUT(6L, "登出");

    public final Long status;

    public final String desc;

    QqRobotUserEnum(Long status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
