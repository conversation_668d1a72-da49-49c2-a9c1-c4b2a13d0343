package com.fenxiang.social.engine.base.enums;

import com.fenxiang.social.engine.base.enums.sku.OrderDyStatus;
import com.fenxiang.social.engine.base.enums.sku.OrderPddStatus;
import com.fenxiang.social.engine.base.enums.sku.OrderTkStatus;
import com.fenxiang.social.engine.base.enums.sku.SkuJDStatus;

/**
 * 省订单状态
 */
public enum QqShengOrderStatusEnum {

    WAIT_PAY(10, "待付款"),
    PAY_SUCCESS(20, "已付款"),
    FINISHED(30, "已完成"),
    INVALID(40, "无效");

    public final Integer code;

    public final String desc;

    QqShengOrderStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static QqShengOrderStatusEnum getByCode(Integer code) {
        for (QqShengOrderStatusEnum status : QqShengOrderStatusEnum.values()) {
            if (status.code.equals(code)) {
                return status;
            }
        }
        return null;
    }

    public static QqShengOrderStatusEnum fromHWOrderStatus(MovieOrderPayStatusEnum movieStatus) {
        if (movieStatus == null) {
            return INVALID;
        }
        switch (movieStatus) {
            case WAIT_PAY:
                return WAIT_PAY;
            case GENERATE_SUCCESS:
                return FINISHED;
            case WAIT_GENERATE:
                return PAY_SUCCESS;
            case REFUNDED:
            case CLOSE:
            default:
                return INVALID;
        }
    }

    public static QqShengOrderStatusEnum fromSkuJdStatus(SkuJDStatus skuJDStatus) {
        if (skuJDStatus == null) {
            return INVALID;
        }
        switch (skuJDStatus) {
            case PENDING_PAYMENT:
                return WAIT_PAY;
            case PAID_DEPOSIT:
            case PAID:
                return PAY_SUCCESS;
            case COMPLETED:
                return FINISHED;
            default:
                return INVALID;
        }
    }

    public static QqShengOrderStatusEnum fromSkuPddStatus(OrderPddStatus orderPddStatus) {
        if (orderPddStatus == null) {
            return INVALID;
        }
        switch (orderPddStatus){
            case PAID:
            case GROUP_SUCCESS:
            case RECEIVED:
            case AUDIT_SUCCESS:
                return PAY_SUCCESS;
            case SETTLED:
                return FINISHED;
            case AUDIT_FAIL:
            case PUNISHED:
            default:
                return INVALID;
        }
    }

    public static QqShengOrderStatusEnum fromSkuTkStatus(OrderTkStatus orderTkStatus) {
        if (orderTkStatus == null) {
            return INVALID;
        }
        switch (orderTkStatus){
            case PAID:
                return PAY_SUCCESS;
            case SUCCESS:
            case SETTLED:
                return FINISHED;
            case INVALID:
            default:
                return INVALID;
        }
    }

    public static QqShengOrderStatusEnum fromDyStatus(OrderDyStatus orderDyStatus){
        if(orderDyStatus == null){
            return INVALID;
        }
        switch (orderDyStatus){
            case PAID:
                return PAY_SUCCESS;
            case SETTLED:
                return FINISHED;
            case REFUNDED:
            default:
                return INVALID;
        }
    }

    public static boolean isInvalid(int code){
        return INVALID.code.equals(code);
    }
}
