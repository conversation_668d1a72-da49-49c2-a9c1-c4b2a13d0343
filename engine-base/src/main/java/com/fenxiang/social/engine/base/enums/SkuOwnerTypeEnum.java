package com.fenxiang.social.engine.base.enums;


import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * @Description 账号租用状态枚举
 * <AUTHOR>
 * @date 2023/7/17
 **/
public enum SkuOwnerTypeEnum {
    OWN("g", "自营"),

    POP("p", "pop"),

    ;
    public final String code;
    public final String desc;

    SkuOwnerTypeEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SkuOwnerTypeEnum codeOf(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }

        return Arrays.stream(values()).filter(e -> code.equals(e.code)).findFirst().orElse(null);
    }
}