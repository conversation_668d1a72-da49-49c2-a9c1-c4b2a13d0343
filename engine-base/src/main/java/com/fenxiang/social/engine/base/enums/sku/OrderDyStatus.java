package com.fenxiang.social.engine.base.enums.sku;

import lombok.Getter;

/**
 * 订单状态枚举
 */
@Getter
public enum OrderDyStatus {

    PAID(1, "已付款"),
    SETTLED(2, "已结算"), 
    REFUNDED(3, "已退款");

    /**
     * 状态码
     */
    private final int code;

    /**
     * 状态描述
     */
    private final String desc;

    OrderDyStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举值，如果没找到返回null
     */
    public static OrderDyStatus getByCode(int code) {
        for (OrderDyStatus status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断状态码是否有效
     *
     * @param code 状态码
     * @return true:有效 false:无效
     */
    public static boolean isValid(Integer code) {
        if (code == null) {
            return false;
        }
        return getByCode(code) != null;
    }
}