package com.fenxiang.social.engine.base.enums.sku;

import com.fenxiang.social.engine.base.enums.QqShengOrderStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 饿了么订单状态枚举
 */
@Getter
@AllArgsConstructor
public enum EleOrderStatus {
    
    UN_VALID(0L, "已失效"),
    ORDER(1L, "已下单"),
    PAY(2L, "已付款"),
    ON_SALE_REFUND(3L, "售中退"),
    RECEIVE(4L, "已收货"),
    SALE_COMPLETED_REFUND(5L, "售后退"),
    ;

    private final Long code;
    private final String desc;

    /**
     * 根据状态码获取枚举
     */
    public static EleOrderStatus getByCode(Long code) {
        if (code == null) {
            return null;
        }
        for (EleOrderStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断是否为指定状态
     */
    public static boolean is(Long code, EleOrderStatus status) {
        return status != null && status.getCode().equals(code);
    }

    /**
     * 判断是否为已完成状态
     */
    public static boolean isCompleted(Long code) {
        return is(code, RECEIVE);
    }

    /**
     * 判断是否为退款状态
     */
    public static boolean isRefund(Long code) {
        return is(code, ON_SALE_REFUND) || is(code, SALE_COMPLETED_REFUND);
    }

    /**
     * 判断订单是否有效
     */
    public static boolean isValid(Long code) {
        return !is(code, UN_VALID);
    }

    /**
     * 获取状态描述
     */
    public static String getDesc(Long code) {
        EleOrderStatus status = getByCode(code);
        return status != null ? status.getDesc() : "";
    }

    public static QqShengOrderStatusEnum toQqShengOrderStatus(EleOrderStatus status) {
        if (status == null){
            return QqShengOrderStatusEnum.INVALID;
        }
        switch (status) {
            case ORDER:
            case PAY:
            case ON_SALE_REFUND:
                return QqShengOrderStatusEnum.PAY_SUCCESS;
            case RECEIVE:
                return QqShengOrderStatusEnum.FINISHED;
            case UN_VALID:
            case SALE_COMPLETED_REFUND:
                return QqShengOrderStatusEnum.INVALID;
        }
        return QqShengOrderStatusEnum.INVALID;
    }
}