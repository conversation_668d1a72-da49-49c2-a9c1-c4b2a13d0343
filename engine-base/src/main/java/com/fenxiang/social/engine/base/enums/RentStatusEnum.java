package com.fenxiang.social.engine.base.enums;


import java.util.Arrays;

/**
 * @Description 账号租用状态枚举
 * <AUTHOR>
 * @date 2023/7/17
 **/
public enum RentStatusEnum {
    DELETED(-1, "已删除"),

    FREE(0, "未配对"),

    BOUND(1, "已配对"),

    ;
    public final Integer code;
    public final String desc;

    RentStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static RentStatusEnum statusOf(Integer code) {
        if (code == null) {
            return null;
        }

        return Arrays.stream(values()).filter(e -> e.code.intValue() == code.intValue()).findFirst().orElse(null);
    }
}