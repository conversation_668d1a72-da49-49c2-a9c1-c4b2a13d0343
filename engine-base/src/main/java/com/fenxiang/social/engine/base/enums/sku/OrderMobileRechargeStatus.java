package com.fenxiang.social.engine.base.enums.sku;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.fenxiang.social.engine.base.enums.QqShengOrderStatusEnum;
import lombok.Getter;

import java.util.List;
import java.util.Set;

@Getter
public enum OrderMobileRechargeStatus {

    CANCELLED(-1, "已取消"),
    PROCESSING(0, "充值中"),
    SUCCESS(1, "充值成功"),
    FAILED(2, "充值失败"),
    PARTIAL_SUCCESS(3, "部分成功"),

    WAIT_PAY(11, "待支付"),
    PAY_SUCCESS(12, "支付成功"),
    RPC_FAILED(13, "RPC失败"),
    RPC_CANCELLING(14, "RPC取消中"),
    UNKNOWN(99, "未知状态"),

    ;

    private final int code;
    private final String desc;

    OrderMobileRechargeStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static OrderMobileRechargeStatus fromCode(int code) {
        for (OrderMobileRechargeStatus state : values()) {
            if (state.getCode() == code) {
                return state;
            }
        }
        return UNKNOWN;
    }

    public static OrderMobileRechargeStatus fromCode(String code) {
        if(StrUtil.isBlank(code)){
            return UNKNOWN;
        }
        int codeValue = Integer.parseInt(code);
        return fromCode(codeValue);
    }

    public static QqShengOrderStatusEnum toQqShengOrderStatus(OrderMobileRechargeStatus state) {
        if(state == null){
            return QqShengOrderStatusEnum.INVALID;
        }
        switch (state) {
            case WAIT_PAY:
                return QqShengOrderStatusEnum.WAIT_PAY;
            case PAY_SUCCESS:
            case PROCESSING:
                return QqShengOrderStatusEnum.PAY_SUCCESS;
            case SUCCESS:
            case PARTIAL_SUCCESS:
                return QqShengOrderStatusEnum.FINISHED;
            case CANCELLED:
            case FAILED:
            case RPC_FAILED:
                return QqShengOrderStatusEnum.INVALID;
        }
        return QqShengOrderStatusEnum.INVALID;
    }

    public static List<Integer> unFinishedStates() {
        return ListUtil.of(WAIT_PAY.getCode(),
                PAY_SUCCESS.getCode(),
                PROCESSING.getCode(),
                RPC_FAILED.getCode(),
                RPC_CANCELLING.getCode(),
                UNKNOWN.getCode()
        );
    }

    public static boolean needRefundStates(int state) {
        OrderMobileRechargeStatus status = fromCode(state);
        return status == FAILED
                || status == PARTIAL_SUCCESS
                || status == CANCELLED;
    }

    public static List<Integer> checkExistStates() {
        return ListUtil.of(
                PAY_SUCCESS.getCode(),
                PROCESSING.getCode(),
                RPC_FAILED.getCode(),
                RPC_CANCELLING.getCode(),
                UNKNOWN.getCode()
        );
    }

    @Getter
    public static enum PayStatus{
        WAIT_PAY(1, "待支付"),
        PAY_SUCCESS(2, "支付成功"),
        PAY_FAIL(3, "支付失败"),
        UNKNOWN(0, "无状态"),
        ;
        private final int code;
        private final String desc;

        PayStatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }
        public static PayStatus fromCode(int code) {
            for (PayStatus state : values()) {
                if (state.getCode() == code) {
                    return state;
                }
            }
            return UNKNOWN;
        }
    }

    @Getter
    public static enum RefundStatus{
        WAIT_REFUND(1, "待退单"),
        REFUNDING(2, "退单中"),
        REFUND_SUCCESS(3, "退单成功"),
        REFUND_FAIL(4, "退单失败"),
        RPC_CANCELLING(5, "退单申请中"),
        UNKNOWN(0, "无状态"),

        ;
        private final int code;
        private final String desc;

        RefundStatus(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static RefundStatus fromCode(int code) {
            for (RefundStatus state : values()) {
                if (state.getCode() == code) {
                    return state;
                }
            }
            return UNKNOWN;
        }

        public static boolean isRefundSuccess(int code) {
            return code == REFUND_SUCCESS.code;
        }
    }
}