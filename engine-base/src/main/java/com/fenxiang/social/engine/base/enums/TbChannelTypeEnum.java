package com.fenxiang.social.engine.base.enums;

import lombok.ToString;

/**
 * 结核病通道类型列举
 *
 * <AUTHOR>
 * @date 2024/08/14
 */
@ToString
public enum TbChannelTypeEnum {
    /**
     * 键盘
     */
    DEFAULT(0L, "默认-人工录入"),
    REAL_TIME(1L, "实时榜"),
    ALL_DAY(2L, "全天榜"),
    HOT_PUSH(3L, "热推榜"),
    HOT_SEARCH(7L, "综合热搜榜"),
    LOWER_PRICE_HIGH_COMMISSION(15L, "低价高佣榜"),
    LOWER_PRICE(99L, "9.9包邮"),
    HAO_DAB_KU_SUBSIDY(70L,"好单库-百亿补贴榜单");
    /**
     * 查询选择
     */
    public final Long role;
    /**
     * 描述
     */
    public final String msg;


    TbChannelTypeEnum(Long role, String msg) {
        this.role = role;
        this.msg = msg;
    }

    public Long getRole() {
        return role;
    }

    public String getMsg() {
        return msg;
    }
}
