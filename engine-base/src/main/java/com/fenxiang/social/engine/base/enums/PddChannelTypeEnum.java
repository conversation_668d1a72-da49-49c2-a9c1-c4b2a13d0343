package com.fenxiang.social.engine.base.enums;

import lombok.ToString;

/**
 * pdd通道类型enum
 *
 * <AUTHOR>
 * @date 2024/08/14
 */
@ToString
public enum PddChannelTypeEnum {
    /**
     * 键盘
     */
    DEFAULT(0L, "默认-人工录入"),
    TODAY_SALES(1L, "今日销量榜"),
    RECOMMENDED(3L, "相似商品推荐"),
    GUESS_LIKE(4L, "猜你喜欢"),
    REAL_SALES(5L, "实时热销榜"),
    REAL_EARNINGS(6L, "实时收益榜");
    /**
     * 查询选择
     */
    public final Long role;
    /**
     * 描述
     */
    public final String msg;


    PddChannelTypeEnum(Long role, String msg) {
        this.role = role;
        this.msg = msg;
    }

    public Long getRole() {
        return role;
    }

    public String getMsg() {
        return msg;
    }
}
