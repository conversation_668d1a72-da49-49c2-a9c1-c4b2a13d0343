package com.fenxiang.social.engine.base.enums.sku;

import lombok.Getter;

/**
 * 订单状态枚举
 */
@Getter
public enum OrderPddStatus {
    
    PAID(0, "已支付"),
    GROUP_SUCCESS(1, "已成团"),
    RECEIVED(2, "确认收货"),
    AUDIT_SUCCESS(3, "审核成功"),
    AUDIT_FAIL(4, "审核失败（不可提现）"),
    SETTLED(5, "已经结算"),
    PUNISHED(10, "已处罚");

    /**
     * 状态码
     */
    private final int code;
    
    /**
     * 状态描述
     */
    private final String desc;

    OrderPddStatus(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }



    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return 对应的枚举值，如果没找到返回null
     */
    public static OrderPddStatus getByCode(int code) {
        for (OrderPddStatus status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }

    /**
     * 判断状态码是否有效
     *
     * @param code 状态码
     * @return true:有效 false:无效
     */
    public static boolean isValid(Integer code) {
        if (code == null) {
            return false;
        }
        return getByCode(code) != null;
    }
}