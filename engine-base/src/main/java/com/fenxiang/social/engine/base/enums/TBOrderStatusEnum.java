package com.fenxiang.social.engine.base.enums;

import java.util.Arrays;

/**
 * 淘宝订单状态
 */
public enum TBOrderStatusEnum {

    PAID(12, "订单付款"),
    INVALID(13, "订单失效"),
    SUCCESS(14, "订单成功");

    public final Integer status;

    public final String desc;

    TBOrderStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }

    public static TBOrderStatusEnum statusOf(Integer status) {
        if (status == null) {
            return null;
        }
        return Arrays.stream(TBOrderStatusEnum.values()).filter(orderStatusEnum -> orderStatusEnum.status.equals(orderStatusEnum)).findFirst().orElse(null);
    }
}
